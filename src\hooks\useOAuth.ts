/**
 * OAuth hook
 *
 * This hook provides OAuth configuration and utilities for components
 * that need to implement OAuth authentication.
 */

import { useCallback, useState, useEffect } from 'react'
import { oauthConfig, oauthRedirectUris } from '../config/oauth'
import { FirebaseOAuthHelper } from '../utils/oauth/firebaseOAuth'
import { AppleOAuthHelper } from '../utils/oauth/appleOAuth'
import { OAuthIntegration } from '../utils/oauth/oauthIntegration'
import type { OAuthProvider, OAuthUserData } from '../types/oauth'

/**
 * OAuth hook return type
 */
interface UseOAuthReturn {
  /**
   * Google OAuth configuration
   */
  google: {
    clientId: string
    isConfigured: boolean
    redirectUri: string
  }

  /**
   * Apple OAuth configuration
   */
  apple: {
    teamId: string
    bundleId: string
    isConfigured: boolean
    redirectUri: string
  }

  /**
   * Check if any OAuth provider is available
   */
  hasAnyProvider: boolean

  /**
   * Sign in with Google
   */
  signInWithGoogle: (
    onSuccess?: (userData: OAuthUserData) => void,
    onError?: (error: Error) => void
  ) => Promise<void>

  /**
   * Sign in with Apple
   */
  signInWithApple: (
    onSuccess?: (userData: OAuthUserData) => void,
    onError?: (error: Error) => void
  ) => Promise<void>

  /**
   * Loading state for OAuth operations
   */
  isLoading: OAuthProvider | null

  /**
   * Error state for OAuth operations
   */
  error: string | null
}

/**
 * Hook for OAuth configuration and utilities
 *
 * @returns OAuth configuration and helper functions
 */
export function useOAuth(): UseOAuthReturn {
  const [isLoading, setIsLoading] = useState<OAuthProvider | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Initialize OAuth providers on mount
  useEffect(() => {
    // Skip OAuth initialization in test environments to prevent SSL connection errors
    const isTestEnvironment =
      process.env.NODE_ENV === 'test' ||
      process.env.NEXT_PUBLIC_DISABLE_OAUTH === 'true' ||
      (typeof window !== 'undefined' && (window as any).__DISABLE_OAUTH_FOR_TESTS__) ||
      (typeof navigator !== 'undefined' && (
        navigator.userAgent?.includes('Playwright') ||
        navigator.userAgent?.includes('HeadlessChrome') ||
        navigator.userAgent?.includes('WebKit') && navigator.userAgent?.includes('Safari') && !navigator.userAgent?.includes('Chrome')
      ))

    if (isTestEnvironment) {
      return
    }

    // Initialize Firebase for Google OAuth only
    FirebaseOAuthHelper.initialize().catch((err) => {
      console.error('Failed to initialize Firebase OAuth for Google:', err)
      // Don't set error state as Apple will initialize on-demand
    })
    // Apple OAuth initializes on-demand when sign-in is attempted
  }, [])

  /**
   * Sign in with Google
   */
  const signInWithGoogle = useCallback(
    async (
      onSuccess?: (userData: OAuthUserData) => void,
      onError?: (error: Error) => void
    ) => {
      setIsLoading('google')
      setError(null)

      try {
        await FirebaseOAuthHelper.signInWithGoogle(
          async (userData) => {
            // Handle OAuth success through integration layer
            await OAuthIntegration.handleOAuthSuccess(userData, 'google')
            setIsLoading(null)
            onSuccess?.(userData)
          },
          (err) => {
            const normalizedError = OAuthIntegration.normalizeError(
              err,
              'google'
            )
            setError(normalizedError.message)
            setIsLoading(null)
            // Convert OAuthError to standard Error for callback
            const error = new Error(normalizedError.message)
            Object.assign(error, {
              code: normalizedError.code,
              provider: normalizedError.provider,
            })
            onError?.(error)
          }
        )
      } catch (err) {
        const error =
          err instanceof Error ? err : new Error('Google sign-in failed')
        setError(error.message)
        setIsLoading(null)
        onError?.(error)
      }
    },
    []
  )

  /**
   * Sign in with Apple
   */
  const signInWithApple = useCallback(
    async (
      onSuccess?: (userData: OAuthUserData) => void,
      onError?: (error: Error) => void
    ) => {
      setIsLoading('apple')
      setError(null)

      try {
        // Apple uses direct SDK (not Firebase) - matching mobile app
        await AppleOAuthHelper.signIn(
          async (userData) => {
            // Handle OAuth success through integration layer
            await OAuthIntegration.handleOAuthSuccess(userData, 'apple')
            setIsLoading(null)
            onSuccess?.(userData)
          },
          (err) => {
            const normalizedError = OAuthIntegration.normalizeError(
              err,
              'apple'
            )
            setError(normalizedError.message)
            setIsLoading(null)
            // Convert OAuthError to standard Error for callback
            const error = new Error(normalizedError.message)
            Object.assign(error, {
              code: normalizedError.code,
              provider: normalizedError.provider,
            })
            onError?.(error)
          }
        )
      } catch (err) {
        const error =
          err instanceof Error ? err : new Error('Apple sign-in failed')
        setError(error.message)
        setIsLoading(null)
        onError?.(error)
      }
    },
    []
  )

  return {
    google: {
      clientId: oauthConfig.google.clientId,
      isConfigured: oauthConfig.google.isConfigured(),
      redirectUri: oauthRedirectUris.google(),
    },
    apple: {
      teamId: oauthConfig.apple.teamId,
      bundleId: oauthConfig.apple.bundleId,
      isConfigured: oauthConfig.apple.isConfigured(),
      redirectUri: oauthRedirectUris.apple(),
    },
    hasAnyProvider: oauthConfig.hasAnyProvider(),
    signInWithGoogle,
    signInWithApple,
    isLoading,
    error,
  }
}
