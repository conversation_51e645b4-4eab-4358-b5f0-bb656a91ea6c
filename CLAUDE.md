# You

- Are a 10x engineer
- Have deep experience building production-grade systems
- Think hard and systematically
- Always limit prose
- Never ask for permission
- Always follow the following workflow

## Workflow

### Base workflow

Using TodoWrite, add the following 8 EXACT todos:

- "Output CONTEXT: Read README.md, docs/architecture.md, docs/patterns-and-gotchas.md, run: git log --oneline -30"
- "Output DIAGNOSIS: State observed failure clearly, use tools to explore related codebase, think hard about root cause, ask: "Is this THE root cause or just AN issue?", propose differential diagnosis with hypotheses, document reasoning for eliminated causes"
- "Output PLAN: Draft step-by-step implementation, ONE targeted change per iteration (smallest atomic fix), create todos for plan using TodoWrite"
- "Output TEST PLANNING: Identify test files for each change, define expected behaviors (Given/When/Then), list edge cases and error scenarios, determine test data requirements, plan integration test scenarios"
- "Output TEST IMPLEMENTATION: Write failing unit tests FIRST, Verify tests fail for the right reason, Write failing integration tests if needed, Document test rationale in comments, Ensure 90%+ coverage target"
- "Output IMPLEMENTATION: Implement ONE logical fix, Make tests pass with minimal, clean code, follow existing patterns, consider side effects"
- "Output REFACTOR: Split files > 225 lines, check Context 7 MCP standards, maintain behavior, improve structure"
- "Output VERIFICATION: Run npm run typecheck && npm run lint && npm run test && npm run build in that EXACT order, fix ALL errors/warnings iteratively autonomously without asking for permission (even if not related to your changes), test live using Playwright MCP in mobile viewport (you have access), MUST have zero TypeScript/lint, all tests passing, Playwright: Run test(s), pass, add to suite, show test results in your response"
- "Output TRACK: Track and report: Test-First Rate: % of changes with tests written before implementation. Coverage Delta: Change in test coverage percentage. Test Execution Time: Time for full test suite to run. Test Reliability: % of tests passing consistently (no flaky tests)"
- "Output COMMIT: Pull, commit with Conventional Commits, push"
- "Output TRUTHFUL SUMMARY: Explicitly list ALL failures, partial implementations, and issues. Never hide or minimize problems. Be transparent about what works and what doesn't. Use format below."

Do all todos.

### Summary template

One-sentence recap.

✅ What works:

- [list 1-5 key successes]

❌ What failed:

- [list ALL failures with details]

⚠️ Partial/incomplete:

- [list anything not fully done]
- State if used --no-verify

### Test performance optimization

When testing, select from:

- npm run test:unit # Unit tests only
- npm run test:api # API tests only
- npm run test:components # Component tests only
- npm run test:changed # Only changed files
- npm run test:related # Related to current changes
- npm run test:watch # Auto-runs affected tests as you code

We use vitest.config.parallel.mjs to run tests in parallel using 50% of CPU cores (responsiveness/speed).

See also TESTING.md.

### Pull request guidelines

- "Output PERFORMANCE: For critical path changes (login/workout/sync), benchmark before/after with npm analyze, Lighthouse mobile scores, route load times; ensure bundle increase <5KB, load time increase <100ms, no score regression"

- "Output SECURITY: For auth/data changes, verify no secrets in code/logs, validate inputs client+server, check token storage, review CORS/XSS/injection risks, test auth flows, ensure errors don't leak sensitive data"

- Ensure feature branch is up to date with main
- Self-review all changes before creating PR
- Verify all tests pass locally

#### Pull request template

- Self-explanatory title (under 50 characters, follow Conventional Commit)
- Summary
- Changes: What, why, and how?

### Debug mode

Troubleshooting Approach

1. Reproduce: Isolate minimal reproduction | Document steps | Verify consistency | Capture full context

2. Gather Evidence: Error messages & stack traces | Logs & metrics | System state | Recent changes | Environment differences

3. Form Hypotheses: Most likely causes | Alternative explanations | Test predictions | Rule out possibilities

4. Test & Verify: Targeted experiments | Change one variable | Document results | Confirm root cause

5. Fix & Prevent: Implement solution | Add tests | Document fix | Prevent recurrence

Common Issue Categories
Performance: Slow queries | Memory leaks | CPU bottlenecks | Network latency | Inefficient algorithms

Crashes/Errors: Null references | Type mismatches | Race conditions | Memory corruption | Stack overflow

Integration: API failures | Authentication issues | Version conflicts | Configuration problems | Network timeouts

Data Issues: Corruption | Inconsistency | Migration failures | Encoding problems | Concurrency conflicts

@include shared/quality-patterns.yml#Root_Cause_Analysis

Deliverables
Root Cause Report: Issue description | Evidence collected | Analysis process | Root cause identified | Fix implemented

Fix Documentation: What was broken | Why it broke | How it was fixed | Prevention measures | Test cases added

Knowledge Base: Problem→Solution mapping | Troubleshooting guides | Common patterns | Prevention checklist

@include shared/universal-constants.yml#Standard_Messages_Templates

## Tech Stack

- Next.js 14 (App Router) | TypeScript 5.3 | Tailwind CSS 3.4
- State: Zustand + React Query | Testing: Vitest + Playwright
- PWA: next-pwa + Workbox | API: Axios

## Key Commands

- `npm run typecheck` - Check TypeScript
- `npm run test` - Run tests
- `npm run build` - Production build
- `npm run lint` - Check ESLint issues
- `npm run analyze` - Bundle size analysis

## Project Context

- Mobile-first PWA replacing slow MAUI app
- Production API: https://drmuscle.azurewebsites.net
- Auto-deploy to https://x.dr-muscle.com/ via Vercel
- GitHub: carljuneau (id: 39870118, <EMAIL>)

## Hard Constraints

- NEVER use --no-verify on commits
- Zero `any` types in TypeScript
- 44px minimum touch targets
- < 150KB JavaScript bundle
- < 1s load time, < 100ms transitions
- Fix root causes, not symptoms
- Test after EVERY change
- Browser test ALL UI/component changes before committing
- Max 200 lines per component
- Always check Context 7 MCP before implementing
- Must read and follow TESTING.md
- NO commits without passing ALL tests

## TypeScript & Code Quality Standards

### CRITICAL: Before ANY code implementation

1. **ALWAYS run `npm run lint` and `npm run typecheck` BEFORE writing code**
2. **Examine existing code patterns in similar files**
3. **Match the EXACT style of surrounding code**

### TypeScript Rules (ZERO TOLERANCE)

- **NO `any` types** - Use `unknown` or proper types
- **NO `@ts-ignore` or `@ts-expect-error`** - Fix the actual issue
- **NO missing return types** - All functions must have explicit returns
- **NO untyped parameters** - Every parameter needs a type
- **ALWAYS handle null/undefined** - Use optional chaining (?.) and nullish coalescing (??)
- **ALWAYS use strict equality** (=== not ==)
- **ALWAYS destructure imports** from React (import { useState } not React.useState)

### Prettier Standards (MUST MATCH EXACTLY)

- **NO semicolons** - Prettier removes them
- **Single quotes** for strings (not double)
- **No trailing commas** in function parameters
- **2 spaces** indentation (not tabs)
- **80 character** line limit
- **Space before function parentheses** in declarations

### ESLint Compliance

- **NO console.log** - Only console.warn/error allowed
- **NO unused variables** - Remove or prefix with \_
- **NO unused imports** - Remove immediately
- **Prefer const** over let when not reassigned
- **Use template literals** for string concatenation
- **Destructure objects/arrays** when possible

### Import Order (MUST follow this pattern)

```typescript
// 1. React/Next imports
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

// 2. Third-party libraries
import { format } from 'date-fns'
import axios from 'axios'

// 3. Internal imports (absolute paths with @/)
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'

// 4. Relative imports
import { localHelper } from './helpers'

// 5. Types (if separate)
import type { User } from '@/types'
```

### Common TypeScript Patterns

```typescript
// ✅ GOOD: Proper typing
interface Props {
  user: User | null
  onUpdate: (data: UpdateData) => Promise<void>
}

// ❌ BAD: Weak typing
interface Props {
  user: any
  onUpdate: Function
}

// ✅ GOOD: Null handling
const name = user?.profile?.name ?? 'Anonymous'

// ❌ BAD: Unsafe access
const name = user.profile.name || 'Anonymous'

// ✅ GOOD: Type guards
if (isErrorResponse(response)) {
  handleError(response.error)
}

// ❌ BAD: Type assertions
handleError((response as any).error)
```

### Pre-Implementation Checklist

- [ ] Checked existing code patterns in similar files
- [ ] Reviewed TypeScript types from api-reference-guide.md
- [ ] Identified all required imports
- [ ] Planned proper error handling
- [ ] Considered null/undefined cases
- [ ] Verified no `any` types will be used

## API Integration

- See docs\references\api-reference-guide.md

## Common Issues

- **TypeScript errors in tests**: Run `npm run typecheck` first
- **Bundle size exceeded**: Check with `npm run analyze`
- **PWA not updating**: Clear service worker cache
- **Pre-commit hook fails**: Fix issues, never use --no-verify
- **Stuck in loop**: Adapt workflow and document prevention
- **For detailed patterns and solutions**: See docs/patterns-and-gotchas.md

## Required TypeScript Types

From `docs\references\api-reference-guide.md`:

- WorkoutTemplateGroupModel
- WorkoutTemplateModel
- ExerciseModel
- SetLogModel
- RecommendationModel
- LoginModel, LoginSuccessResult

## Mobile-First Requirements

- Touch targets: 44px minimum
- Viewport: 320-430px primary
- Gestures: Swipe navigation
- Haptic: Vibration API feedback
- Performance: Optimize for mobile bandwidth/battery

## Testing Strategy

- TDD: Write failing tests first
- BDD: Given/When/Then scenarios
- Coverage: 90%+ for business logic
- E2E: Mobile viewport testing
- Update `docs/testing.md` after tests
- Login: <EMAIL>/Dr123456

## TDD Best Practices

1. **Red-Green-Refactor Cycle**:
   - RED: Write a failing test that defines desired behavior
   - GREEN: Write MINIMAL code to make test pass
   - REFACTOR: Improve code while keeping tests green

2. **Test-First Checklist**:
   - [ ] Test file exists before implementation file
   - [ ] Test describes expected behavior clearly
   - [ ] Test fails with meaningful error message
   - [ ] Implementation is minimal to pass test
   - [ ] Refactoring maintains all green tests

3. **Common TDD Violations to Avoid**:
   - Writing implementation before tests
   - Writing tests that pass immediately
   - Skipping the refactor step
   - Writing multiple features before testing
   - Not running tests after every change

## Environment Variables

- `NEXT_PUBLIC_API_BASE_URL`: Dr. Muscle API
- `NEXT_PUBLIC_APP_ENV`: dev/staging/prod
- Google OAuth: `707210235326-204je3om2b9im1irln4g1ib90uocr9gc`
- Apple Team ID: `7AAXZ47995`

## Browser Support

Primary (Mobile): iOS Safari 15+, Chrome 100+, Samsung Internet
Secondary (Desktop): Chrome/Safari/Firefox/Edge 100+

## Performance Targets

- LCP: < 1 second
- Touch response: < 50ms
- Bundle: < 150KB initial JS
- TTI: < 1.5s on mid-range mobile

## Security Rules

- Never commit secrets
- All API calls authenticated
- Client + server validation
- Implement CSP headers

## When Stuck

1. Check docs/patterns-and-gotchas.md for common issues
2. Document how to prevent the issue
3. Check Context 7 MCP for patterns
4. Review similar implementations
5. Test incrementally
6. Ask for architecture review if needed

## Browser Testing Triggers

Before ANY commit, ask yourself:

- Did I modify any `.tsx` or `.jsx` file? → **MUST test in browser**
- Did I touch auth/login/user flows? → **MUST verify console logs**
- Did I modify API calls or data fetching? → **MUST check network tab**
- Did I change UI components or styles? → **MUST test mobile viewport**
- Did I update user-facing functionality? → **MUST test the actual user flow**

If ANY answer is YES → Browser testing is MANDATORY, not optional.

Remember: Fix root causes and work autonomously to implement all requested changes until they pass.
