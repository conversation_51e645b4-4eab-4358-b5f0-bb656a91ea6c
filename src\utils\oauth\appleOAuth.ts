/**
 * Apple OAuth Helper for Dr. Muscle X
 *
 * Handles Apple Sign-In SDK loading, initialization, and authentication flow
 * with comprehensive error handling and performance tracking.
 */

import type {
  OAuthSuccessCallback,
  OAuthErrorCallback,
} from '../../types/oauth'
import type { AppleSignInResponse } from '../../types/appleOAuthTypes'
import { PerformanceMonitor } from '../performance'
import { logger } from '../logger'
import { AppleAuthConfig } from './appleAuth.config'
import { AppleAuthLoader } from './appleAuth.loader'
import { AppleAuthTokenManager } from './appleAuth.tokenManager'
import { AppleAuthErrorHandler } from './appleAuth.errorHandler'

/**
 * Apple OAuth Helper class
 * Provides static methods for Apple Sign-In integration
 */
export class AppleOAuthHelper {
  /**
   * Get the client ID dynamically with debugging
   */
  static getClientId(): string {
    const envValue = process.env.NEXT_PUBLIC_APPLE_SERVICES_ID
    const clientId = envValue || 'com.drmaxmuscle.web'

    if (
      typeof window !== 'undefined' &&
      process.env.NODE_ENV === 'development'
    ) {
      logger.error('[Apple OAuth] getClientId:', {
        envValue,
        clientId,
        allEnvKeys: Object.keys(process.env).filter((k) => k.includes('APPLE')),
      })
    }

    return clientId
  }

  /**
   * Get the redirect URI dynamically
   */
  static getRedirectUri(): string {
    const uri =
      typeof window !== 'undefined'
        ? `${window.location.origin}/auth/apple/callback`
        : 'https://x.dr-muscle.com/auth/apple/callback'

    // Log redirect URI details in development
    if (
      typeof window !== 'undefined' &&
      process.env.NODE_ENV === 'development'
    ) {
      logger.error('[Apple OAuth] Redirect URI:', {
        origin: window.location.origin,
        pathname: '/auth/apple/callback',
        fullUri: uri,
        protocol: window.location.protocol,
        hostname: window.location.hostname,
        port: window.location.port,
      })
    }

    return uri
  }

  // Debug logging for environment variables
  static {
    if (
      typeof window !== 'undefined' &&
      process.env.NODE_ENV === 'development' &&
      !(window as any).__DISABLE_OAUTH_FOR_TESTS__
    ) {
      logger.error('[Apple OAuth] Environment check:', {
        NEXT_PUBLIC_APPLE_SERVICES_ID:
          process.env.NEXT_PUBLIC_APPLE_SERVICES_ID,
        isUsingDefault: !process.env.NEXT_PUBLIC_APPLE_SERVICES_ID,
        windowOrigin: window.location.origin,
      })
    }
  }

  /**
   * Initialize Apple Sign-In
   */
  static async initialize(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    try {
      await AppleAuthLoader.initialize(
        (response) =>
          this.handleSignInResponse(
            response as AppleSignInResponse,
            onSuccess,
            onError
          ),
        (error) => AppleAuthErrorHandler.handleSignInError(error, onError)
      )
    } catch (error) {
      const oauthError = AppleAuthErrorHandler.handleInitError(
        error instanceof Error ? error : new Error('Unknown error')
      )
      onError(oauthError)
    }
  }

  /**
   * Sign in with Apple
   */
  static async signIn(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    logger.log('[Apple OAuth] Sign in initiated')

    try {
      PerformanceMonitor.mark('apple_signin_start')

      // Check if initialized
      if (!AppleAuthLoader.isReady()) {
        logger.log('[Apple OAuth] Not initialized, initializing now...')
        await this.initialize(onSuccess, onError)
      }

      // Ensure AppleID is available
      if (!window.AppleID?.auth) {
        throw new Error('AppleID.auth is not available')
      }

      // Get stored state and nonce
      const state = sessionStorage.getItem('apple_oauth_state')
      const nonce = sessionStorage.getItem('apple_oauth_nonce')

      if (!state || !nonce) {
        logger.log('[Apple OAuth] Missing state or nonce, reinitializing...')
        await this.initialize(onSuccess, onError)
      }

      // Create sign-in configuration
      const signInConfig = {
        clientId: this.getClientId(),
        scope: 'email name',
        redirectURI: this.getRedirectUri(),
        state: state || '',
        nonce: nonce || '',
        usePopup: true,
      }

      // Debug logging only in development
      if (process.env.NODE_ENV === 'development') {
        logger.error('[Apple OAuth] Sign-in configuration:', {
          clientId: signInConfig.clientId,
          redirectURI: signInConfig.redirectURI,
          scope: signInConfig.scope,
          usePopup: signInConfig.usePopup,
          envVar: process.env.NEXT_PUBLIC_APPLE_SERVICES_ID,
          isDefaultClientId: signInConfig.clientId === 'com.drmaxmuscle.max',
          currentOrigin: window.location.origin,
          fullRedirectURI: this.getRedirectUri(),
        })
      }

      try {
        const response = await window.AppleID.auth.signIn(signInConfig)
        // Handle the response
        this.handleSignInResponse(response, onSuccess, onError)
      } catch (signInError) {
        // Enhanced error logging for Apple Sign-In failures
        let errorMessage = 'Unknown error'
        let errorCode: string | undefined
        let errorDetail: string | undefined

        if (signInError instanceof Error) {
          errorMessage = signInError.message
        } else if (
          typeof signInError === 'object' &&
          signInError !== null &&
          'message' in signInError
        ) {
          errorMessage = String(signInError.message)
        }

        if (
          typeof signInError === 'object' &&
          signInError !== null &&
          'code' in signInError
        ) {
          errorCode = String(signInError.code)
        }

        if (
          typeof signInError === 'object' &&
          signInError !== null &&
          'detail' in signInError
        ) {
          errorDetail = String(signInError.detail)
        }
        if (process.env.NODE_ENV === 'development') {
          logger.error('[Apple OAuth] Sign-in failed:', {
            error: signInError,
            message: errorMessage,
            code: errorCode,
            detail: errorDetail,
            configuration: {
              clientId: signInConfig.clientId,
              redirectURI: signInConfig.redirectURI,
              currentURL: window.location.href,
              origin: window.location.origin,
            },
          })
        }

        // Check for specific error messages
        if (
          errorMessage.includes('Invalid client id or web redirect url') &&
          process.env.NODE_ENV === 'development'
        ) {
          logger.error('[Apple OAuth] Configuration mismatch detected!', {
            possibleIssues: [
              'Services ID (clientId) does not match Apple Developer Portal configuration',
              'Redirect URI does not match exactly what is configured in Apple Developer Portal',
              'Domain is not verified in Apple Developer Portal',
              'Protocol mismatch (http vs https)',
            ],
            currentConfig: {
              clientId: signInConfig.clientId,
              redirectURI: signInConfig.redirectURI,
              expectedFormat: 'https://yourdomain.com/auth/apple/callback',
            },
          })
        }

        throw signInError
      }
    } catch (error) {
      logger.log('[Apple OAuth] Sign in failed:', error)
      PerformanceMonitor.mark('apple_signin_error')
      onError({
        code: 'oauth_failed',
        message: error instanceof Error ? error.message : 'Sign in failed',
        provider: 'apple',
        details: error as Record<string, unknown>,
      })
    }
  }

  /**
   * Handle successful sign-in response
   */
  private static async handleSignInResponse(
    response: AppleSignInResponse,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    try {
      logger.error('[Apple OAuth] Processing sign in response:', {
        hasAuthCode: !!response.authorization?.code || !!response.code,
        hasIdToken: !!response.authorization?.id_token || !!response.id_token,
        hasState: !!response.state,
        hasUser: !!response.user,
      })

      // Verify state for CSRF protection
      const storedState = sessionStorage.getItem('apple_oauth_state')
      if (response.state && response.state !== storedState) {
        throw new Error('State mismatch - possible CSRF attack')
      }

      // Extract fields from either response format
      const code = response.authorization?.code || response.code
      const idToken = response.authorization?.id_token || response.id_token

      // Ensure we have required fields
      if (!code || !idToken) {
        throw new Error('Missing authorization code or ID token')
      }

      // Decode the ID token to get user info
      const tokenPayload = AppleAuthTokenManager.decodeJWT(idToken)
      if (!tokenPayload) {
        throw new Error('Failed to decode ID token')
      }

      // Extract user data
      const userData = AppleAuthTokenManager.extractUserData(
        {
          ...response,
          code,
          id_token: idToken,
        },
        tokenPayload
      )

      // Clear stored state and nonce
      sessionStorage.removeItem('apple_oauth_state')
      sessionStorage.removeItem('apple_oauth_nonce')

      logger.log('[Apple OAuth] Sign in successful')
      onSuccess(userData)
    } catch (error) {
      logger.log('[Apple OAuth] Failed to process sign in response:', error)
      onError({
        code: 'oauth_failed',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to process sign in response',
        provider: 'apple',
        details: error as Record<string, unknown>,
      })
    }
  }

  /**
   * Handle redirect callback (for redirect flow)
   */
  static handleRedirectCallback(
    urlParams: URLSearchParams,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): void {
    try {
      const code = urlParams.get('code')
      const idToken = urlParams.get('id_token')
      const state = urlParams.get('state')
      const error = urlParams.get('error')

      if (error) {
        throw new Error(`Authentication error: ${error}`)
      }

      if (!code || !idToken) {
        throw new Error('Missing authorization code or ID token')
      }

      // Verify state
      const storedState = sessionStorage.getItem('apple_oauth_state')
      if (state !== storedState) {
        throw new Error('State mismatch - possible CSRF attack')
      }

      // Decode token and extract user data
      const tokenPayload = AppleAuthTokenManager.decodeJWT(idToken)
      const userData = AppleAuthTokenManager.extractUserData(
        { code, id_token: idToken, state: state || undefined },
        tokenPayload
      )

      // Clear stored state
      sessionStorage.removeItem('apple_oauth_state')
      sessionStorage.removeItem('apple_oauth_nonce')

      onSuccess(userData)
    } catch (error) {
      const oauthError = AppleAuthErrorHandler.handleRedirectError(
        error instanceof Error ? error : new Error('Unknown error')
      )
      onError(oauthError)
    }
  }

  // Re-export utility methods from modules
  static decodeJWT = AppleAuthTokenManager.decodeJWT

  static getConfiguration = AppleAuthConfig.debugConfiguration

  static isAvailable = AppleAuthLoader.isAvailable

  static isReady = AppleAuthLoader.isReady

  static isSdkLoading = () => AppleAuthLoader.getState().isLoading

  static debugConfiguration = AppleAuthConfig.debugConfiguration
}
