/**
 * Apple OAuth Configuration
 * Contains all configuration constants and helper methods
 */

import { logger } from '../logger'

export class AppleAuthConfig {
  static readonly APPLE_SDK_URL =
    'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js'

  /**
   * Get the client ID dynamically with debugging
   */
  static getClientId(): string {
    const envValue = process.env.NEXT_PUBLIC_APPLE_SERVICES_ID
    // TODO: Remove hardcode after env var is properly loading in Vercel
    const clientId = 'com.drmaxmuscle.web' // Temporarily hardcoded - should be: envValue || 'com.drmaxmuscle.max'

    if (
      typeof window !== 'undefined' &&
      process.env.NODE_ENV === 'development'
    ) {
      logger.error('[Apple OAuth] getClientId:', {
        envValue,
        clientId,
        allEnvKeys: Object.keys(process.env).filter((k) => k.includes('APPLE')),
      })
    }

    return clientId
  }

  /**
   * Get the redirect URI dynamically
   */
  static getRedirectUri(): string {
    const uri =
      typeof window !== 'undefined'
        ? `${window.location.origin}/auth/apple/callback`
        : 'https://x.dr-muscle.com/auth/apple/callback'

    // Log redirect URI details in development
    if (
      typeof window !== 'undefined' &&
      process.env.NODE_ENV === 'development'
    ) {
      logger.error('[Apple OAuth] Redirect URI:', {
        origin: window.location.origin,
        pathname: '/auth/apple/callback',
        fullUri: uri,
        protocol: window.location.protocol,
        hostname: window.location.hostname,
        port: window.location.port,
      })
    }

    return uri
  }

  /**
   * Generate a random state parameter for CSRF protection
   */
  static generateState(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    )
  }

  /**
   * Generate a random nonce for token validation
   */
  static generateNonce(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    )
  }

  /**
   * Debug configuration
   */
  static debugConfiguration(): {
    clientId: string
    redirectUri: string
    environment: string
    isConfigured: boolean
    sdkUrl: string
  } {
    return {
      clientId: this.getClientId(),
      redirectUri: this.getRedirectUri(),
      environment: process.env.NODE_ENV || 'production',
      isConfigured: true,
      sdkUrl: this.APPLE_SDK_URL,
    }
  }
}

// Debug logging for environment variables
if (
  typeof window !== 'undefined' &&
  process.env.NODE_ENV === 'development' &&
  !(window as any).__DISABLE_OAUTH_FOR_TESTS__
) {
  logger.error('[Apple OAuth] Environment check:', {
    NEXT_PUBLIC_APPLE_SERVICES_ID: process.env.NEXT_PUBLIC_APPLE_SERVICES_ID,
    isUsingDefault: !process.env.NEXT_PUBLIC_APPLE_SERVICES_ID,
    windowOrigin: window.location.origin,
  })
}
