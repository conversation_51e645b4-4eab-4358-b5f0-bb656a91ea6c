import { test, expect } from '@playwright/test'

// Simple login helper
async function login(page: any) {
  await page.goto('/login')
  await page
    .getByRole('textbox', { name: 'Email' })
    .fill('<EMAIL>')
  await page.locator('#password').fill('Dr123456')

  // Wait for button to be enabled and click
  const loginButton = page.locator('button[type="submit"]')
  await expect(loginButton).toBeEnabled({ timeout: 5000 })
  await loginButton.click()

  // Wait for navigation
  await page.waitForURL('/program', { timeout: 10000 })
}

test.describe('Critical E2E Flows - Simplified', () => {
  test('Login and start workout', async ({ page }) => {
    // Login
    await login(page)

    // Verify we're on program page
    await expect(page).toHaveURL('/program')

    // Start workout - find any start button
    const startButton = page.getByRole('button', { name: /start/i }).first()
    await expect(startButton).toBeVisible({ timeout: 5000 })
    await startButton.click()

    // Wait for workout page
    await page.waitForURL(/\/workout/, { timeout: 10000 })

    // Verify we reached workout page
    await expect(page).toHaveURL(/\/workout/)
  })

  test('Complete a set', async ({ page }) => {
    // Login and navigate to workout
    await login(page)

    // Start workout
    const startButton = page
      .getByRole('button', { name: /start|continue/i })
      .first()
    await startButton.click()
    await page.waitForURL(/\/workout/, { timeout: 10000 })

    // Click first exercise
    const firstExercise = page.locator('[data-testid*="exercise"]').first()
    await firstExercise.click()

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise/, { timeout: 10000 })

    // Find and click save button
    const saveButton = page.getByRole('button', { name: /save/i }).first()
    await expect(saveButton).toBeVisible({ timeout: 10000 })
    await saveButton.click()

    // Verify save was successful (button changes or navigation occurs)
    await page.waitForTimeout(2000) // Give time for save to complete
  })
})
