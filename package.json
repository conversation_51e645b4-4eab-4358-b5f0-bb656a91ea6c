{"name": "dr-muscle-x-webapp", "version": "0.1.0", "private": true, "description": "This is the Dr. Muscle X Web Application repository.", "main": "index.js", "directories": {"doc": "docs"}, "scripts": {"dev": "next dev", "build": "next build", "build:pwa": "next build && next export", "start": "next start", "lint": "next lint", "test": "vitest --config vitest.config.parallel.mjs", "test:watch": "vitest --config vitest.config.parallel.mjs --watch", "test:sequential": "vitest --config vitest.config.sequential.mjs", "test:sequential:watch": "vitest --config vitest.config.sequential.mjs --watch", "test:ci": "vitest run --config vitest.config.parallel.mjs", "test:unit": "vitest run --config vitest.config.parallel.mjs src/**/*.test.{ts,tsx}", "test:api": "vitest run --config vitest.config.parallel.mjs src/api/**/*.test.ts", "test:components": "vitest run --config vitest.config.parallel.mjs src/components/**/*.test.tsx", "test:hooks": "vitest run --config vitest.config.parallel.mjs src/hooks/**/*.test.ts", "test:stores": "vitest run --config vitest.config.parallel.mjs src/stores/**/*.test.ts", "test:coverage": "vitest run --coverage", "test:changed": "vitest run --config vitest.config.parallel.mjs --changed", "test:related": "vitest run --config vitest.config.parallel.mjs --related", "test:mobile": "vitest run --config vitest.mobile.config.ts", "test:e2e": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test", "test:e2e:ui": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --ui", "test:e2e:debug": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --debug", "test:production": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --config=playwright.production.config.ts", "test:production:ui": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --config=playwright.production.config.ts --ui", "test:production:stats": "node scripts/test-production-stats.js", "playwright:install": "playwright install", "typecheck": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "prettier:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "lint:security": "eslint . --ext .js,.jsx,.ts,.tsx --plugin security --rule 'security/detect-object-injection: error'", "test:e2e:critical": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --config=playwright.ci.config.ts --grep @critical", "test:critical-flows": "npm run test:warmups && node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test tests/e2e/critical-flows.spec.ts tests/e2e/workout-arrow-adjustments.spec.ts --config=playwright.ci.optimized.config.ts --project=\"Mobile Safari Full\" --reporter=list", "test:warmups": "npm run test:warmups:unit && npm run test:warmups:components", "test:warmups:unit": "vitest run src/utils/__tests__/warmupCalculator.test.ts src/hooks/__tests__/useSetScreenLogic.test.ts", "test:warmups:components": "vitest run src/components/workout/__tests__/ExerciseSetsGrid.integration.test.tsx src/components/workout/__tests__/SetScreenWithGrid.test.tsx", "test:e2e:full": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --config=playwright.ci.config.ts", "lighthouse:ci": "lhci autorun --config=lighthouse-ci.config.js", "test:accessibility": "node --expose-gc --max_old_space_size=8192 ./node_modules/.bin/playwright test --config=playwright.a11y.config.ts", "test:api:contracts": "vitest run --config vitest.api.config.ts", "audit:licenses": "license-checker --production --failOn GPL", "analyze": "cross-env ANALYZE=true next build", "lighthouse": "lhci autorun", "lighthouse:mobile": "lhci autorun --config=lighthouse-mobile.config.js", "prepare": "husky", "generate-icons": "node scripts/generate-icons.js", "test:dev": "./scripts/dev-test.sh", "test:dev:full": "./scripts/dev-test.sh --full", "test:dev:critical": "./scripts/dev-test.sh --critical", "test:dev:quick": "./scripts/dev-test.sh --skip-build", "test:quick": "npm run typecheck && npm run lint && npm run test:changed", "test:fast": "vitest run --config vitest.config.parallel.mjs --reporter=dot", "test:low-cpu": "vitest run --config vitest.config.sequential.mjs --reporter=dot", "test:before-commit": "npm run test:quick && npm run test:dev:critical", "test:before-push": "npm run test:dev:full", "check:file-sizes": "node scripts/check-file-sizes.js", "test:webkit-setup": "node scripts/test-webkit-setup.js", "test:webkit-fix": "node scripts/test-webkit-fix.js"}, "repository": {"type": "git", "url": "git+https://github.com/dr-muscle/DrMuscleWebApp.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/dr-muscle/DrMuscleWebApp/issues"}, "homepage": "https://github.com/dr-muscle/DrMuscleWebApp#readme", "dependencies": {"@next/bundle-analyzer": "^15.3.4", "@tanstack/react-query": "^5.81.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.6.0", "axios": "^1.10.0", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "firebase": "^11.10.0", "form-data": "^4.0.3", "framer-motion": "^12.23.7", "happy-dom": "^18.0.1", "husky": "^9.1.7", "immer": "^10.1.1", "lint-staged": "^16.1.2", "lucide-react": "^0.525.0", "next": "^15.3.4", "next-pwa": "^5.6.0", "node-fetch": "^2.7.0", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "tailwind-merge": "^3.3.1", "typescript": "^5.8.3", "vitest": "^3.2.4", "workbox-webpack-plugin": "^7.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@lhci/cli": "^0.14.0", "@playwright/test": "^1.54.1", "@testing-library/dom": "^10.4.0", "@testing-library/user-event": "^14.6.1", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "axe-playwright": "^2.1.0", "axios-mock-adapter": "^2.1.0", "cross-env": "^7.0.3", "eslint-config-next": "^15.3.4", "eslint-plugin-security": "^3.0.1", "glob": "^11.0.3", "license-checker": "^25.0.1", "playwright": "^1.54.1", "playwright-test": "^14.1.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}